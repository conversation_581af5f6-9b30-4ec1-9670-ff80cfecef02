import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      res.data.items = getSafetyData(res.data, "items", []);
    }
    return res;
  },
  post: res => res
};
const typeMapConfig = {
  getVersionTree: function() {
    return {
      method: "get",
      url: `${api.getVersionTree}`
    };
  },
  getYearTree() {
    return {
      method: "get",
      url: `${api.getYearTree}`
    };
  },
  getPeriodTree() {
    return {
      method: "get",
      url: `${api.getPeriodTree}`
    };
  },
  getScopeTree() {
    return {
      method: "get",
      url: `${api.getScopeTree}`
    };
  },
  getOrganizationMenuTree(params) {
    return {
      method: "post",
      url: `${api.getOrganizationMenuTree}`,
      params
    };
  },
  addSameLevelNode(params) {
    return {
      method: "post",
      url: `${api.addSameLevelNode}`,
      params
    };
  },
  addChildLevelNode(params) {
    return {
      method: "post",
      url: `${api.addChildLevelNode}`,
      params
    };
  },
  moveNodeByType(params) {
    return {
      method: "post",
      url: `${api.moveNodeByType}`,
      params
    };
  },
  deleteNodeById({ id, isAllScope }) {
    const baseUrl = `${api.deleteNodeById}/${id}`;
    const url = isAllScope ? baseUrl + "/allScope" : baseUrl;
    return {
      method: "delete",
      url
    };
  },
  copyMonthFramework(params) {
    return {
      method: "post",
      url: `${api.copyMonthFramework}`,
      params
    };
  },
  // 股权管理 复制持股比例
  copyShareholdingRatio: function(params) {
    return {
      method: "post",
      url: `${api.copyShareholdingRatio}`,
      params
    };
  },
  getOrganizationPage(params) {
    return {
      method: "post",
      url: `${api.getOrganizationPage}`,
      params
    };
  },
  saveOrganizationTableData(data) {
    return {
      method: "post",
      url: `${api.saveOrganizationTableData}`,
      params: data
    };
  },
  getTableOrganizationList(data) {
    return {
      method: "post",
      url: `${api.getTableOrganizationList}`,
      params: data
    };
  },
  getEquityChart(data) {
    return {
      method: "post",
      url: `${api.getEquityChart}`,
      params: data
    };
  },
  copyFinalShareholding(data) {
    return {
      method: "post",
      url: `${api.copyFinalShareholding}`,
      params: data
    };
  },
  // 组织架构导出、下载模板
  dowloadOrganizationTemplate: function(params) {
    return {
      method: "post",
      url: `${api.dowloadOrganizationTemplate}`,
      responseType: "blob",
      params
    };
  },
  // 组织架构导入
  importOrganizationTemplate: function(params) {
    return {
      method: "post",
      url: `${api.importOrganizationTemplate}`,
      params
    };
  },
  // 获取导入结果
  getTaskOrganizationResult: function(id) {
    return {
      method: "get",
      url: `${api.getTaskOrganizationResult}?taskId=${id}`
    };
  },
  // 下载导入结果模板
  exportOrganizationResult: function(id) {
    return {
      method: "get",
      responseType: "blob",
      url: `${api.exportOrganizationResult}?taskId=${id}`
    };
  },
  batchCopyShareholding(params) {
    return {
      method: "post",
      url: `${api.batchCopyShareholding}`,
      params
    };
  },
  batchScopeCopyShareholding(params) {
    return {
      method: "post",
      url: `${api.batchScopeCopyShareholding}`,
      params
    };
  },
  updateOrganizationEntityChildNum(params) {
    return {
      method: "post",
      url: `${api.updateOrganizationEntityChildNum}`,
      params
    };
  },
  // 获取股权管理功能权限
  getOrganizationAuth: function(refId) {
    const resourceType = "MetadataObject";
    const bizAppId = UrlUtils.getQuery("appId");
    return {
      method: "get",
      iscode: false,
      url: `${api.getEquityAuth}?query=refId='${refId}' and resourceType='${resourceType}' and privilegeCode in ('organization-read','organization-add','organization-copyMonthFramework','organization-delete','organization-edit','organization-import','organization-export') and bizAppId='${bizAppId}'`
    };
  },
  allPermission() {
    return {
      method: "get",
      url: `${api.allPermission}`
    };
  },
  // 获取权益抵消合并规则
  getAppParams(code = "equityOffsetConsolidationRules") {
    return {
      method: "get",
      url: `${api.appParams}?query=paramCode='${code}'`
    };
  },
  checkOrganizationProcessExists(params) {
    return {
      method: "post",
      url: `${api.checkOrganizationProcessExists}`,
      params
    };
  }
};

/**
 * @desc 组织架构相关请求处理
 * @param {string} method
 * @param {object} params
 */
const organizationService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default organizationService;
